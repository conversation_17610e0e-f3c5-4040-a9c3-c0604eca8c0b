# Developer Guide: Adding a New Connector

This guide provides a complete walkthrough for creating and integrating a new connector into the system. Please follow these steps carefully to ensure your connector is compliant with our Unified Connector Architecture.

## 1. Introduction

Our connector architecture is designed to be modular, scalable, and easy to maintain. By following this guide, you will create a self-contained connector that can be seamlessly registered and used by the application.

## 2. Connector Types & Categories

Before you begin, you must identify the type and category of your connector.

*   **Connector Type:**
    *   `structured`: For data sources with a well-defined schema (e.g., APIs, databases).
    *   `unstructured`: For data sources with free-form content (e.g., documents, web pages).
*   **Category:** Choose one of the predefined categories that best fits your connector (e.g., `Task Management`, `Documentation`, `Code Repository`). There are 14 categories in total in file `modules/connectors/utilities/constant/connector_category.py`.

This information will be used in the `connector_info.json` file.

## 3. Step 1: Create the Directory Structure

All connectors reside in the `modules/connectors/handlers/` directory. Create a new directory for your connector (e.g., `my_connector`) with the following structure:

```
modules/connectors/handlers/
└── my_connector/
    ├── __init__.py
    ├── connection.py               # Handles connection logic and health checks.
    ├── service.py                  # Contains the main connector implementation.
    ├── constants/                  # Directory for node and relationship definitions
    │   ├── __init__.py
    │   ├── entities.py
    │   └── relationships.py
    ├── schema.py                   # Defines Pydantic schemas for data validation.
    ├── tests/
    │   └── test_my_connector.py    # Unit and integration tests.
    └── connector_info.json         # Metadata for the registration script.
```

## 4. Step 2: Implement the Connector Service

The core logic of your connector resides in the `service.py` file. Here, you will create a class that inherits from `BaseConnector`.

**`service.py` Implementation:**

1.  **Create the Class:** Define a class (e.g., `MyConnectorService`) that inherits from `BaseConnector`.
2.  **Set Connector Type:** Set the `CONNECTOR_TYPE` class variable to either `"structured"` or `"unstructured"`.
3.  **Implement Abstract Methods:** Provide concrete implementations for all abstract methods defined in the `BaseConnector` class.

```python
# In modules/connectors/handlers/my_connector/service.py

from modules.connectors.base import BaseConnector
from typing import Any, Dict, List, Iterator

class MyConnectorService(BaseConnector):
    """
    Service implementation for My Connector.
    """
    CONNECTOR_TYPE = "unstructured"

    def connect(self) -> Any:
        # Implementation here
        pass

    def get_connector(self) -> dict:
        # Implementation here
        pass

    def fetch_data(self) -> Iterator[Dict[str, Any]]:
        # Implementation here
        pass

    def fetch_data_by_id(self, id: str) -> Dict[str, Any]:
        # Implementation here
        pass

    def sync(self):
        # Implementation here
        pass

    def sync_by_id(self, id: str):
        # Implementation here
        pass

    def store_context(self, data: Any):
        # Implementation here
        pass

    def search(self, query: str) -> ConnectorSearchResponse:
        # Implementation here
        pass
```

## 5. Step 3: Define Nodes and Relationships

The registration script dynamically loads node and relationship definitions. Your responsibility is to create the files where these definitions live.

*   **For `unstructured` connectors:** This step is not required. The registration script automatically loads a standard set of definitions from `modules/connectors/utilities/constant/`.
*   **For `structured` connectors:** You must define the specific nodes and relationships for your connector inside the `constants` directory you created in Step 1.

**File Structure:**

```
<connector_name>/
└── constants/
    ├── __init__.py
    ├── entities.py
    └── relationships.py
```

**Implementation (`entities.py`):**

Create an `Enum` named `EntityType` and a function `get_all_entity_types()` that returns all values.

```python
# In <connector_name>/constants/entities.py
from enum import Enum

class EntityType(Enum):
    JIRA_PROJECT = "JiraProject"
    JIRA_TICKET = "JiraTicket"

def get_all_entity_types():
    return {e.value for e in EntityType}
```

**Implementation (`relationships.py`):**

Create an `Enum` named `RelationshipType` and a function `get_all_relationship_types()`.

```python
# In <connector_name>/constants/relationships.py
from enum import Enum

class RelationshipType(Enum):
    HAS_TICKET = "HAS_TICKET"
    ASSIGNED_TO = "ASSIGNED_TO"

def get_all_relationship_types():
    return {r.value for r in RelationshipType}
```

## 6. Step 4: Define Connector Metadata

Create a `connector_info.json` file in your connector's root directory. **Crucially, always leave the `nodes` and `relationships` fields as empty arrays.** They will be populated dynamically by the registration script.

**Example `connector_info.json`:**

```json
{
  "source_type": "jira",
  "name": "Jira",
  "connector_type": "structured",
  "category": "Task Management",
  "icon": "base64_encoded_icon_string",
  "description": "Jira is a project and issue tracking tool widely used by engineering, product, and QA teams to manage software development workflows, sprints, and bug tracking.",
  "purpose": "This connector integrates with Jira to fetch structured data about projects, issues (tasks, bugs, stories), comments, and attachments. It enables querying and analyzing task workflows, priorities, and developer activity by storing the data in a knowledge graph.",
  "nodes": [], // Always leave as empty array
  "relationships": [], // Always leave as empty array
  "example_usage": "Useful for analyzing engineering productivity, sprint progress, bug severity trends, and ownership of tickets. Enables searching issues across all Jira projects in an organization using semantic or graph-based search.",
  "example_queries": [
    "Find all open bugs assigned to John Doe in the last sprint",
    "List tasks with high priority in Project Phoenix",
    "Which developer created the most issues last month?",
    "Show all stories linked to epic JIRA-2411",
    "What are the blockers in the current sprint for Team Alpha?",
    "Fetch all tasks that have been in 'In Progress' state for more than 5 days",
    "What bugs are frequently commented on or reopened?",
    "List all Jira issues modified in the last 7 days",
    "Which issues are assigned to users who are no longer active?",
    "Get a timeline of all tickets moved from 'To Do' to 'Done' in the past month"
  ]
}
```

## 7. Step 5: Register the Connector

Once your connector is implemented and the metadata is defined, you must register it using the provided CLI script.

Run the following command from the project's root directory:

```bash
python modules/connectors/register_connector.py --path modules/connectors/handlers/my_connector
```

This script will read your `connector_info.json`, validate it, and register the connector in the central registry (Postgres/Pinecone).

## 8. Step 6: Implement Search Method

Add a simplified search method to your connector. This is the basic boilerplate that all connectors must implement :

```python
def search(self, query: str) -> ConnectorSearchResponse:
    """
    Basic search method implementation
    Override this method with your specific search logic
    """
    try:
        start_time = datetime.now()
        
        # Basic validation
        if not query or not query.strip():
            raise ValueError("Query cannot be empty")
        
        # TODO: Implement your specific search logic here
        # This is where you would:
        # 1. Parse the query for your domain
        # 2. Execute search against your data source
        # 3. Convert results to SearchResultItem format
        
        # Placeholder implementation
        search_results = []  # Replace with actual search results
        
        # Calculate metrics
        execution_time = (datetime.now() - start_time).total_seconds() * 1000
        metrics = SearchMetrics(
            execution_time_ms=execution_time,
            total_results_found=len(search_results),
            results_returned=len(search_results)
        )
        
        # Return standardized response
        return ConnectorSearchResponse(
            status=SearchStatus.SUCCESS,
            query=query,
            results=search_results,
            total_count=len(search_results),
            connector_info=self._get_connector_info(),
            metrics=metrics
        )
        
    except ValueError as e:
        return self._create_error_response(query, "VALIDATION_ERROR", str(e), start_time)
    except Exception as e:
        return self._create_error_response(query, "SEARCH_ERROR", str(e), start_time)

def _get_connector_info(self) -> Dict[str, str]:
    """Return connector information for responses"""
    return {
        "source_type": self.source_type,
        "name": self.connector_name,
        "version": self.version,
        "type": self.CONNECTOR_TYPE
    }

def _create_error_response(self, query: str, error_code: str, error_message: str, start_time: datetime) -> ConnectorSearchResponse:
    """Create standardized error response"""
    execution_time = (datetime.now() - start_time).total_seconds() * 1000
    
    return ConnectorSearchResponse(
        status=SearchStatus.ERROR,
        query=query,
        connector_info=self._get_connector_info(),
        error=SearchError(
            error_code=error_code,
            error_message=error_message,
            error_type="validation_error" if error_code == "VALIDATION_ERROR" else "internal_error"
        ),
        metrics=SearchMetrics(
            execution_time_ms=execution_time,
            total_results_found=0,
            results_returned=0
        )
    )
```

**Note:** For detailed search implementation patterns, query parsing, security considerations, and advanced features, refer to the separate search implementation guide document.

## 9. Step 7: Write Tests

Create comprehensive tests for your connector to ensure reliability and maintainability.

**Test Structure:**

```
<connector_name>/
└── tests/
    ├── __init__.py
    ├── test_service.py
    ├── test_search.py
    └── test_integration.py
```

**Example Test (`test_service.py`):**

```python
import unittest
from unittest.mock import Mock, patch
from your_connector.service import YourConnectorService

class TestYourConnectorService(unittest.TestCase):
    def setUp(self):
        self.config = {
            'api_key': 'test_key',
            'base_url': 'https://api.example.com'
        }
        self.service = YourConnectorService(self.config)
    
    def test_search_basic_query(self):
        """Test basic search functionality"""
        with patch.object(self.service, '_execute_query') as mock_execute:
            mock_execute.return_value = [
                {'id': '1', 'title': 'Test Item', 'description': 'Test Description'}
            ]
            
            result = self.service.search("test query")
            
            self.assertEqual(result.status, SearchStatus.SUCCESS)
            self.assertEqual(len(result.results), 1)
            self.assertEqual(result.results[0].title, 'Test Item')
    
    def test_search_empty_query(self):
        """Test search with empty query"""
        result = self.service.search("")
        
        self.assertEqual(result.status, SearchStatus.ERROR)
        self.assertEqual(result.error.error_code, "VALIDATION_ERROR")

if __name__ == '__main__':
    unittest.main()
```

Ensure you cover the following aspects in your tests:

*   Connection logic (`connect`).
*   Data fetching (`fetch_data`, `fetch_data_by_id`).
*   Data processing and transformation logic.
*   Error handling.
*   Search functionality (`search`).

## 10. Overall Development and Registration Workflow

The following diagram illustrates the end-to-end process for adding a new connector.

**Workflow Diagram:**

```mermaid
graph TD
    A[Start: Read rules.md] --> B{Create Directory Structure};
    B --> C{Define Nodes & Relationships in constants/};
    C --> D[Implement Connector in service.py];
    D --> E{Implement all methods from BaseConnector};
    E --> F[Define metadata in connector_info.json];
    F --> G{Run `python modules/connectors/register_connector.py`};
    G --> H[Connector registered in Postgres/Pinecone];
    H --> I[Connector available via ConnectorFactory];
```

By following this guide, you will create a high-quality, maintainable connector that integrates smoothly into our system.